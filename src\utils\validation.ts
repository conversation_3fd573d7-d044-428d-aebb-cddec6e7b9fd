import { ValidationError } from '@/types';

// 用户名验证
export const validateUsername = (username: string): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  if (!username) {
    errors.push({ field: 'username', message: '用户名不能为空' });
  } else if (username.length < 3) {
    errors.push({ field: 'username', message: '用户名至少需要3个字符' });
  } else if (username.length > 20) {
    errors.push({ field: 'username', message: '用户名不能超过20个字符' });
  } else if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    errors.push({ field: 'username', message: '用户名只能包含字母、数字和下划线' });
  }
  
  return errors;
};

// 密码验证
export const validatePassword = (password: string): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  if (!password) {
    errors.push({ field: 'password', message: '密码不能为空' });
  } else if (password.length < 8) {
    errors.push({ field: 'password', message: '密码至少需要8个字符' });
  } else if (password.length > 50) {
    errors.push({ field: 'password', message: '密码不能超过50个字符' });
  } else {
    // 检查密码强度
    const hasLowerCase = /[a-z]/.test(password);
    const hasUpperCase = /[A-Z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    const strengthCount = [hasLowerCase, hasUpperCase, hasNumbers, hasSpecialChar].filter(Boolean).length;
    
    if (strengthCount < 2) {
      errors.push({ 
        field: 'password', 
        message: '密码强度不足，建议包含大小写字母、数字和特殊字符' 
      });
    }
  }
  
  return errors;
};

// 项目名称验证
export const validateProjectName = (name: string): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  if (!name) {
    errors.push({ field: 'name', message: '项目名称不能为空' });
  } else if (name.length < 1) {
    errors.push({ field: 'name', message: '项目名称不能为空' });
  } else if (name.length > 50) {
    errors.push({ field: 'name', message: '项目名称不能超过50个字符' });
  } else if (!/^[\u4e00-\u9fa5a-zA-Z0-9_\-\s]+$/.test(name)) {
    errors.push({ field: 'name', message: '项目名称只能包含中文、字母、数字、下划线、连字符和空格' });
  }
  
  return errors;
};

// 命名空间名称验证
export const validateNamespaceName = (name: string): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  if (!name) {
    errors.push({ field: 'name', message: '命名空间名称不能为空' });
  } else if (name.length < 1) {
    errors.push({ field: 'name', message: '命名空间名称不能为空' });
  } else if (name.length > 30) {
    errors.push({ field: 'name', message: '命名空间名称不能超过30个字符' });
  } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(name)) {
    errors.push({ field: 'name', message: '命名空间名称必须以字母开头，只能包含字母、数字和下划线' });
  }
  
  return errors;
};

// 图名称验证
export const validateDiagramName = (name: string): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  if (!name) {
    errors.push({ field: 'name', message: '图名称不能为空' });
  } else if (name.length < 1) {
    errors.push({ field: 'name', message: '图名称不能为空' });
  } else if (name.length > 30) {
    errors.push({ field: 'name', message: '图名称不能超过30个字符' });
  } else if (!/^[\u4e00-\u9fa5a-zA-Z0-9_\-\s]+$/.test(name)) {
    errors.push({ field: 'name', message: '图名称只能包含中文、字母、数字、下划线、连字符和空格' });
  }
  
  return errors;
};

// 通用表单验证函数
export const validateForm = (
  data: Record<string, string>,
  validators: Record<string, (value: string) => ValidationError[]>
): ValidationError[] => {
  const allErrors: ValidationError[] = [];
  
  Object.entries(validators).forEach(([field, validator]) => {
    const value = data[field] || '';
    const fieldErrors = validator(value);
    allErrors.push(...fieldErrors);
  });
  
  return allErrors;
};

// 检查是否有验证错误
export const hasValidationErrors = (errors: ValidationError[]): boolean => {
  return errors.length > 0;
};

// 获取特定字段的错误信息
export const getFieldError = (errors: ValidationError[], field: string): string | undefined => {
  const error = errors.find(e => e.field === field);
  return error?.message;
};

// 清除特定字段的错误
export const clearFieldErrors = (errors: ValidationError[], field: string): ValidationError[] => {
  return errors.filter(e => e.field !== field);
};
