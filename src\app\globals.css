@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
}

/* IDE Layout Styles */
.ide-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.ide-header {
  height: 60px;
  border-bottom: 1px solid #e5e7eb;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.ide-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.ide-sidebar {
  width: 300px;
  border-right: 1px solid #e5e7eb;
  background: #f9fafb;
  overflow-y: auto;
}

.ide-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ide-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ide-preview {
  width: 400px;
  border-left: 1px solid #e5e7eb;
  background: #ffffff;
}

.ide-status-bar {
  height: 24px;
  background: #007acc;
  color: white;
  display: flex;
  align-items: center;
  padding: 0 16px;
  font-size: 12px;
}

/* Tree Styles */
.tree-node {
  padding: 4px 8px;
  cursor: pointer;
  user-select: none;
}

.tree-node:hover {
  background: #e5e7eb;
}

.tree-node.selected {
  background: #dbeafe;
  color: #1d4ed8;
}

.tree-node-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tree-node-icon {
  width: 16px;
  height: 16px;
}

/* Tab Styles */
.tab-bar {
  display: flex;
  background: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
}

.tab {
  padding: 8px 16px;
  border-right: 1px solid #e5e7eb;
  background: #f3f4f6;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab.active {
  background: #ffffff;
  border-bottom: 1px solid #ffffff;
}

.tab-close {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
}

.tab-close:hover {
  background: #e5e7eb;
  opacity: 1;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  min-width: 400px;
  max-width: 500px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  margin-bottom: 16px;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.modal-body {
  margin-bottom: 24px;
}

.modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-ghost {
  background: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-ghost:hover {
  background: #f3f4f6;
}

/* Context Menu */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 150px;
}

.context-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
}

.context-menu-item:hover {
  background: #f3f4f6;
}

.context-menu-separator {
  height: 1px;
  background: #e5e7eb;
  margin: 4px 0;
}

/* Auth Pages */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-card {
  background: white;
  border-radius: 12px;
  padding: 32px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.auth-title {
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 24px;
  color: #1f2937;
}

.auth-link {
  color: #3b82f6;
  text-decoration: none;
  font-size: 14px;
}

.auth-link:hover {
  text-decoration: underline;
}
