'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toast';
import { useApp } from '@/contexts/AppContext';
import { authApi, setAuthToken } from '@/utils/api';
import { validateUsername, validatePassword, hasValidationErrors, getFieldError } from '@/utils/validation';
import { ValidationError } from '@/types';

const LoginPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showToast } = useToast();
  const { state, dispatch } = useApp();
  
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 检查是否已登录
  useEffect(() => {
    if (state.auth.isAuthenticated && !state.auth.isLoading) {
      router.push('/workspace');
    }
  }, [state.auth.isAuthenticated, state.auth.isLoading, router]);

  // 显示退出登录提示
  useEffect(() => {
    const loggedOut = searchParams.get('logged_out');
    if (loggedOut === 'true') {
      showToast({
        type: 'info',
        title: '已安全退出',
        message: '您已成功退出登录',
        duration: 3000,
      });
    }
  }, [searchParams, showToast]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除该字段的错误
    setErrors(prev => prev.filter(error => error.field !== field));
  };

  const validateForm = (): boolean => {
    const usernameErrors = validateUsername(formData.username);
    const passwordErrors = validatePassword(formData.password);
    
    const allErrors = [...usernameErrors, ...passwordErrors];
    setErrors(allErrors);
    
    return !hasValidationErrors(allErrors);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await authApi.login(formData.username, formData.password);
      
      if (response.success && response.data) {
        // 保存认证令牌
        setAuthToken(response.data.token);
        
        // 更新应用状态
        dispatch({ type: 'SET_AUTH_USER', payload: response.data.user });
        
        showToast({
          type: 'success',
          title: '登录成功',
          message: `欢迎回来，${response.data.user.username}！`,
          duration: 3000,
        });
        
        // 跳转到工作区
        router.push('/workspace');
      } else {
        showToast({
          type: 'error',
          title: '登录失败',
          message: response.error || '用户名或密码错误',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: '登录失败',
        message: '网络错误，请检查网络连接后重试',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 如果正在加载认证状态，显示加载界面
  if (state.auth.isLoading) {
    return (
      <div className="auth-container">
        <div className="auth-card">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">正在加载...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="text-center mb-8">
          <h1 className="auth-title">登录</h1>
          <p className="text-gray-600">
            登录到您的 SysML v2 建模平台
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Input
            label="用户名"
            type="text"
            placeholder="请输入用户名"
            value={formData.username}
            onChange={(e) => handleInputChange('username', e.target.value)}
            error={getFieldError(errors, 'username')}
            required
            autoComplete="username"
          />

          <Input
            label="密码"
            type="password"
            placeholder="请输入密码"
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            error={getFieldError(errors, 'password')}
            required
            autoComplete="current-password"
          />

          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isSubmitting}
            className="w-full"
            disabled={isSubmitting}
          >
            {isSubmitting ? '登录中...' : '登录'}
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            没有账号？
            <Link href="/register" className="auth-link ml-1">
              去注册
            </Link>
          </p>
        </div>

        <div className="mt-4 text-center">
          <a href="#" className="text-sm auth-link">
            忘记密码？
          </a>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
