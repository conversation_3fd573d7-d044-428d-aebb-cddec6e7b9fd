'use client';

import React, { useRef, useState } from 'react';
import Editor, { Monaco } from '@monaco-editor/react';
import { useApp } from '@/contexts/AppContext';
import { useToast } from '@/components/ui/Toast';
import { EditorTab, DiagnosticMessage } from '@/types';
import { sysmlApi, diagramApi } from '@/utils/api';
import { debounce } from '@/utils/helpers';

interface CodeEditorProps {
  activeTab: EditorTab | null;
}

const CodeEditor: React.FC<CodeEditorProps> = ({ activeTab }) => {
  const { state, dispatch } = useApp();
  const { showToast } = useToast();
  const editorRef = useRef<any>(null);
  const monacoRef = useRef<Monaco | null>(null);
  const [aiPrompt, setAiPrompt] = useState('输入修改意见...');
  const [isAiGenerating, setIsAiGenerating] = useState(false);

  // SysML v2 语言配置
  const configureSysMLLanguage = (monaco: Monaco) => {
    // 注册 SysML 语言
    monaco.languages.register({
      id: 'sysml',
      extensions: ['.sysml', '.sysmlv2'],
      aliases: ['SysML', 'sysml']
    });

    // SysML v2 关键字
    const sysmlKeywords = [
      'package', 'part', 'attribute', 'port', 'connection', 'interface',
      'requirement', 'constraint', 'action', 'state', 'transition',
      'allocation', 'verification', 'view', 'viewpoint', 'rendering',
      'analysis', 'case', 'concern', 'frame', 'function', 'item',
      'metadata', 'occurrence', 'usage', 'definition', 'feature',
      'multiplicity', 'redefinition', 'subsetting', 'specialization',
      'conjugation', 'disjoining', 'unioning', 'intersecting', 'differencing',
      'abstract', 'alias', 'all', 'and', 'as', 'assign', 'assoc',
      'beahvior', 'bind', 'by', 'chains', 'classifier', 'comment',
      'composite', 'conjugate', 'conjugates', 'disjoint', 'end',
      'expr', 'false', 'featured', 'featuring', 'filter', 'first',
      'flow', 'for', 'from', 'hastype', 'if', 'implies', 'import',
      'in', 'inout', 'intersects', 'inv', 'inverse', 'istype',
      'language', 'member', 'namespace', 'nonunique', 'not', 'null',
      'of', 'or', 'ordered', 'out', 'overlaps', 'owns', 'readonly',
      'redefines', 'ref', 'rep', 'return', 'specializes', 'subsets',
      'succession', 'then', 'to', 'true', 'unions', 'variation',
      'via', 'xor'
    ];

    // 语法高亮配置
    monaco.languages.setMonarchTokensProvider('sysml', {
      keywords: sysmlKeywords,
      typeKeywords: [
        'Boolean', 'Integer', 'Real', 'String', 'UnlimitedNatural'
      ],
      operators: [
        '=', '>', '<', '!', '~', '?', ':', '==', '<=', '>=', '!=',
        '&&', '||', '++', '--', '+', '-', '*', '/', '&', '|', '^', '%',
        '<<', '>>', '>>>', '+=', '-=', '*=', '/=', '&=', '|=', '^=',
        '%=', '<<=', '>>=', '>>>='
      ],
      symbols: /[=><!~?:&|+\-*\/\^%]+/,
      escapes: /\\(?:[abfnrtv\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,
      tokenizer: {
        root: [
          [/[a-z_$][\w$]*/, {
            cases: {
              '@typeKeywords': 'keyword',
              '@keywords': 'keyword',
              '@default': 'identifier'
            }
          }],
          [/[A-Z][\w\$]*/, 'type.identifier'],
          { include: '@whitespace' },
          [/[{}()\[\]]/, '@brackets'],
          [/[<>](?!@symbols)/, '@brackets'],
          [/@symbols/, {
            cases: {
              '@operators': 'operator',
              '@default': ''
            }
          }],
          [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
          [/0[xX][0-9a-fA-F]+/, 'number.hex'],
          [/\d+/, 'number'],
          [/[;,.]/, 'delimiter'],
          [/"([^"\\]|\\.)*$/, 'string.invalid'],
          [/"/, { token: 'string.quote', bracket: '@open', next: '@string' }],
          [/'[^\\']'/, 'string'],
          [/(')(@escapes)(')/, ['string', 'string.escape', 'string']],
          [/'/, 'string.invalid']
        ],
        comment: [
          [/[^\/*]+/, 'comment'],
          [/\/\*/, 'comment', '@push'],
          ["\\*/", 'comment', '@pop'],
          [/[\/*]/, 'comment']
        ],
        string: [
          [/[^\\"]+/, 'string'],
          [/@escapes/, 'string.escape'],
          [/\\./, 'string.escape.invalid'],
          [/"/, { token: 'string.quote', bracket: '@close', next: '@pop' }]
        ],
        whitespace: [
          [/[ \t\r\n]+/, 'white'],
          [/\/\*/, 'comment', '@comment'],
          [/\/\/.*$/, 'comment'],
        ],
      },
    });
  };

  // Monaco Editor 配置
  const handleEditorWillMount = (monaco: Monaco) => {
    monacoRef.current = monaco;
    configureSysMLLanguage(monaco);

    // 定义深色主题
    monaco.editor.defineTheme('sysml-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'keyword', foreground: '569cd6' },
        { token: 'operator', foreground: 'd4d4d4' },
        { token: 'number', foreground: 'b5cea8' },
        { token: 'string', foreground: 'ce9178' },
        { token: 'comment', foreground: '6a9955' },
        { token: 'identifier', foreground: '9cdcfe' },
        { token: 'type.identifier', foreground: '4ec9b0' },
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editorLineNumber.foreground': '#858585',
        'editorCursor.foreground': '#aeafad',
        'editor.selectionBackground': '#264f78',
        'editor.lineHighlightBackground': '#2a2d2e',
      }
    });
  };

  // Monaco Editor 挂载后的处理
  const handleEditorDidMount = (editor: any, monaco: Monaco) => {
    editorRef.current = editor;

    // 添加保存快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });

    // 监听内容变化
    editor.onDidChangeModelContent(() => {
      const content = editor.getValue();
      debouncedContentChange(content);
    });
  };

  // 防抖的内容变化处理
  const debouncedContentChange = debounce(async (content: string) => {
    if (!activeTab) return;

    // 更新标签页内容
    dispatch({
      type: 'UPDATE_EDITOR_TAB',
      payload: {
        id: activeTab.id,
        updates: {
          content,
          isDirty: true,
        },
      },
    });

    // 验证代码
    try {
      const response = await sysmlApi.validateCode(content);
      if (response.success && response.data) {
        const diagnostics: DiagnosticMessage[] = response.data.errors.map((error: any, index: number) => ({
          id: `diagnostic-${index}`,
          severity: error.severity || 'error',
          message: error.message || '语法错误',
          line: error.line || 1,
          column: error.column || 1,
          source: 'SysML Parser',
        }));

        dispatch({ type: 'SET_DIAGNOSTICS', payload: diagnostics });
      }
    } catch (error) {
      console.error('Code validation failed:', error);
    }
  }, 500);

  // 保存文件
  const handleSave = async () => {
    if (!activeTab || !activeTab.diagramId) return;

    try {
      const content = editorRef.current?.getValue() || '';
      const response = await diagramApi.saveDiagram(activeTab.diagramId, content);

      if (response.success) {
        dispatch({
          type: 'UPDATE_EDITOR_TAB',
          payload: {
            id: activeTab.id,
            updates: { isDirty: false },
          },
        });

        showToast({
          type: 'success',
          message: '文件已保存',
          duration: 2000,
        });
      } else {
        showToast({
          type: 'error',
          title: '保存失败',
          message: response.error || '保存文件时发生错误',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: '保存失败',
        message: '网络错误，请重试',
      });
    }
  };

  // 生成 SVG 图
  const handleGenerateDiagram = async () => {
    if (!activeTab) return;

    try {
      const content = editorRef.current?.getValue() || '';

      // 先验证代码
      const validateResponse = await sysmlApi.validateCode(content);
      if (!validateResponse.success || !validateResponse.data?.isValid) {
        showToast({
          type: 'error',
          title: '代码验证失败',
          message: '请先修复代码中的错误',
        });
        return;
      }

      // 生成 SVG
      const response = await sysmlApi.generateSVG(content);
      if (response.success && response.data) {
        showToast({
          type: 'success',
          message: 'SVG 图生成成功',
          duration: 2000,
        });

        // 这里可以将 SVG 数据传递给预览组件
        // 暂时在控制台输出
        console.log('Generated SVG:', response.data.svg);
      } else {
        showToast({
          type: 'error',
          title: '生成失败',
          message: response.error || '生成 SVG 图时发生错误',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: '生成失败',
        message: '网络错误，请重试',
      });
    }
  };

  // AI 生成代码
  const handleAIGenerate = async () => {
    if (!aiPrompt || aiPrompt.trim() === '' || aiPrompt === '输入修改意见...') {
      showToast({
        type: 'warning',
        title: '提示',
        message: '请输入修改意见或需求描述',
      });
      return;
    }

    setIsAiGenerating(true);

    try {
      const currentCode = editorRef.current?.getValue() || '';
      const response = await sysmlApi.generateCodeWithCustomPrompt(aiPrompt, currentCode);

      if (response.success && response.data) {
        if (editorRef.current) {
          editorRef.current.setValue(response.data.code);
        }

        // 更新标签页内容
        if (activeTab) {
          dispatch({
            type: 'UPDATE_EDITOR_TAB',
            payload: {
              id: activeTab.id,
              updates: {
                content: response.data.code,
                isDirty: true,
              },
            },
          });
        }

        showToast({
          type: 'success',
          message: 'AI 代码生成完成',
          duration: 2000,
        });

        // 清空提示词输入框
        setAiPrompt('输入修改意见...');
      } else {
        showToast({
          type: 'error',
          title: 'AI 生成失败',
          message: response.error || 'AI 生成代码时发生错误',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: 'AI 生成失败',
        message: '网络错误，请重试',
      });
    } finally {
      setIsAiGenerating(false);
    }
  };

  if (!activeTab) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">欢迎使用 SysML v2 编辑器</h3>
          <p className="text-gray-500">选择或创建一个文件开始编辑</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* 编辑器工具栏 */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-50 border-b">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">{activeTab.title}</span>
          {activeTab.isDirty && <span className="text-orange-500">●</span>}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleSave}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            title="保存 (Ctrl+S)"
          >
            保存
          </button>
          <button
            onClick={handleAIGenerate}
            disabled={isAiGenerating}
            className={`px-3 py-1 text-sm rounded ${
              isAiGenerating
                ? 'bg-purple-400 text-white cursor-not-allowed'
                : 'bg-purple-600 text-white hover:bg-purple-700'
            }`}
            title="AI 生成"
          >
            {isAiGenerating ? '生成中...' : 'AI 生成'}
          </button>
          <button
            onClick={handleGenerateDiagram}
            className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
            title="生成图"
          >
            生成图
          </button>
        </div>
      </div>

      {/* AI 提示词输入区域 */}
      <div className="px-4 py-3 bg-gray-100 border-b">
        <div className="flex items-center space-x-3">
          <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
            AI 提示词：
          </label>
          <input
            type="text"
            value={aiPrompt}
            onChange={(e) => setAiPrompt(e.target.value)}
            onFocus={() => {
              if (aiPrompt === '输入修改意见...') {
                setAiPrompt('');
              }
            }}
            onBlur={() => {
              if (aiPrompt.trim() === '') {
                setAiPrompt('输入修改意见...');
              }
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !isAiGenerating) {
                handleAIGenerate();
              }
            }}
            placeholder="输入修改意见..."
            className={`flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
              aiPrompt === '输入修改意见...' ? 'text-gray-400' : 'text-gray-900'
            }`}
            disabled={isAiGenerating}
          />
          <div className="text-xs text-gray-500 whitespace-nowrap">
            按 Enter 快速生成
          </div>
        </div>
        <div className="mt-2 text-xs text-gray-600">
          💡 提示：描述您想要的代码修改或新功能，AI 将基于当前代码进行生成或修改
        </div>
      </div>

      {/* Monaco Editor 区域 */}
      <div className="flex-1">
        <Editor
          height="100%"
          defaultLanguage="sysml"
          defaultValue={activeTab.content}
          value={activeTab.content}
          theme="sysml-dark"
          beforeMount={handleEditorWillMount}
          onMount={handleEditorDidMount}
          options={{
            fontSize: 14,
            lineNumbers: 'on',
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            wordWrap: 'on',
            folding: true,
            lineDecorationsWidth: 10,
            lineNumbersMinChars: 3,
            glyphMargin: true,
            contextmenu: true,
            mouseWheelZoom: true,
            smoothScrolling: true,
            cursorBlinking: 'blink',
            cursorSmoothCaretAnimation: 'on',
            renderLineHighlight: 'line',
            selectOnLineNumbers: true,
            roundedSelection: false,
            colorDecorators: true,
            codeLens: true,
            automaticLayout: true,
            suggest: {
              showKeywords: true,
              showSnippets: true,
              showFunctions: true,
              showConstructors: true,
              showFields: true,
              showVariables: true,
              showClasses: true,
              showStructs: true,
              showInterfaces: true,
              showModules: true,
              showProperties: true,
              showEvents: true,
              showOperators: true,
              showUnits: true,
              showValues: true,
              showConstants: true,
              showEnums: true,
              showEnumMembers: true,
              showColors: true,
              showFiles: true,
              showReferences: true,
              showFolders: true,
              showTypeParameters: true,
            }
          }}
          loading={
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-500">加载编辑器中...</div>
            </div>
          }
        />
      </div>
    </div>
  );
};

export default CodeEditor;
