'use client';

import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { DiagnosticMessage } from '@/types';

const ProblemsPanel: React.FC = () => {
  const { state } = useApp();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [filter, setFilter] = useState<'all' | 'error' | 'warning' | 'info'>('all');

  const diagnostics = state.diagnostics;

  // 过滤诊断信息
  const filteredDiagnostics = diagnostics.filter(diagnostic => {
    if (filter === 'all') return true;
    return diagnostic.severity === filter;
  });

  // 统计各类型问题数量
  const counts = {
    error: diagnostics.filter(d => d.severity === 'error').length,
    warning: diagnostics.filter(d => d.severity === 'warning').length,
    info: diagnostics.filter(d => d.severity === 'info').length,
  };

  // 获取严重程度图标
  const getSeverityIcon = (severity: DiagnosticMessage['severity']) => {
    switch (severity) {
      case 'error':
        return (
          <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  // 获取严重程度文本颜色
  const getSeverityTextColor = (severity: DiagnosticMessage['severity']) => {
    switch (severity) {
      case 'error': return 'text-red-700';
      case 'warning': return 'text-yellow-700';
      case 'info': return 'text-blue-700';
    }
  };

  // 点击问题项，跳转到对应行
  const handleProblemClick = (diagnostic: DiagnosticMessage) => {
    // 这里应该通知编辑器跳转到指定行
    console.log('Jump to line:', diagnostic.line, 'column:', diagnostic.column);
  };

  // 清除所有问题
  const handleClearAll = () => {
    // dispatch({ type: 'CLEAR_DIAGNOSTICS' });
  };

  return (
    <div className="border-t bg-white">
      {/* 问题面板头部 */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-50 border-b">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="text-gray-600 hover:text-gray-800"
          >
            <svg 
              className={`w-4 h-4 transform transition-transform ${isCollapsed ? '-rotate-90' : ''}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          <h3 className="text-sm font-medium text-gray-700">
            问题 ({filteredDiagnostics.length})
          </h3>
          
          {/* 问题统计 */}
          <div className="flex items-center space-x-3 text-xs">
            {counts.error > 0 && (
              <span className="flex items-center space-x-1 text-red-600">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span>{counts.error}</span>
              </span>
            )}
            
            {counts.warning > 0 && (
              <span className="flex items-center space-x-1 text-yellow-600">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <span>{counts.warning}</span>
              </span>
            )}
            
            {counts.info > 0 && (
              <span className="flex items-center space-x-1 text-blue-600">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <span>{counts.info}</span>
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* 过滤器 */}
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="text-xs border border-gray-300 rounded px-2 py-1"
          >
            <option value="all">全部</option>
            <option value="error">错误</option>
            <option value="warning">警告</option>
            <option value="info">信息</option>
          </select>

          {/* 清除按钮 */}
          {diagnostics.length > 0 && (
            <button
              onClick={handleClearAll}
              className="text-xs text-gray-600 hover:text-gray-800 px-2 py-1 rounded hover:bg-gray-200"
              title="清除所有问题"
            >
              清除
            </button>
          )}
        </div>
      </div>

      {/* 问题列表 */}
      {!isCollapsed && (
        <div className="max-h-48 overflow-y-auto">
          {filteredDiagnostics.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {diagnostics.length === 0 ? (
                <div>
                  <svg className="w-8 h-8 mx-auto mb-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-sm">没有发现问题</p>
                </div>
              ) : (
                <p className="text-sm">当前过滤条件下没有问题</p>
              )}
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredDiagnostics.map((diagnostic) => (
                <div
                  key={diagnostic.id}
                  className="p-3 hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleProblemClick(diagnostic)}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-0.5">
                      {getSeverityIcon(diagnostic.severity)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <p className={`text-sm ${getSeverityTextColor(diagnostic.severity)}`}>
                        {diagnostic.message}
                      </p>
                      
                      <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                        <span>行 {diagnostic.line}, 列 {diagnostic.column}</span>
                        {diagnostic.source && (
                          <span>{diagnostic.source}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProblemsPanel;
