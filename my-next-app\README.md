# SysML v2 建模平台

基于 Next.js 和 React 的 Web 端 SysML v2 建模工具，提供完整的项目管理、代码编辑、图形预览和 AI 辅助功能。

## ✨ 功能特性

### 🔐 用户认证
- 用户注册和登录
- 会话管理和自动重定向
- 安全的认证状态管理

### 📁 项目管理
- 创建、重命名、删除项目
- 树形结构项目浏览器
- 命名空间和图文件管理
- 右键上下文菜单操作

### 💻 代码编辑
- 多标签页编辑器界面
- SysML v2 代码编辑支持
- 实时语法验证和错误提示
- 代码自动保存功能

### 🎨 图形预览
- SVG 图形实时渲染
- 缩放、平移交互功能
- 自适应窗口大小
- 高质量图形输出

### 🤖 AI 辅助
- AI 代码生成功能
- 智能代码补全建议
- 自然语言到代码转换

### 🛠️ 开发工具
- 问题面板显示语法错误
- 状态栏显示编辑信息
- 快捷键支持
- 响应式设计

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装和运行

```bash
# 进入项目目录
cd React/my-next-app

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

访问 http://localhost:3000 开始使用。

### 测试账号
- 用户名: `admin` / 密码: `password123`
- 用户名: `developer` / 密码: `password123`

## 📋 页面结构

### 认证页面
- **登录页面** (`/login`): 用户登录界面
- **注册页面** (`/register`): 新用户注册界面

### 工作区页面
- **主工作区** (`/workspace`): IDE 风格的建模环境
  - 左侧：项目资源管理器
  - 中间：代码编辑器和标签页
  - 右侧：图形预览面板
  - 底部：问题诊断面板

## 🏗️ 技术架构

### 前端技术栈
- **Next.js 15**: React 全栈框架
- **React 19**: 用户界面库
- **TypeScript**: 类型安全的 JavaScript
- **Tailwind CSS 4**: 实用优先的 CSS 框架

### 状态管理
- **React Context**: 全局状态管理
- **useReducer**: 复杂状态逻辑处理
- **本地存储**: 认证令牌持久化

### 组件设计
- **模块化组件**: 可复用的 UI 组件库
- **响应式布局**: 适配不同屏幕尺寸
- **无障碍设计**: 遵循 WCAG 指南

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router 页面
│   ├── login/             # 登录页面
│   ├── register/          # 注册页面
│   ├── workspace/         # 工作区页面
│   ├── layout.tsx         # 根布局组件
│   ├── page.tsx           # 首页（重定向逻辑）
│   └── globals.css        # 全局样式定义
├── components/            # React 组件库
│   ├── auth/              # 认证相关组件
│   ├── ui/                # 通用 UI 组件
│   └── workspace/         # 工作区专用组件
├── contexts/              # React Context 定义
├── types/                 # TypeScript 类型定义
└── utils/                 # 工具函数库
    ├── api.ts             # API 调用封装
    ├── mockData.ts        # 开发模拟数据
    ├── validation.ts      # 表单验证逻辑
    └── helpers.ts         # 通用工具函数
```

## 🎯 核心组件

### UI 组件库
- **Button**: 多样式按钮组件
- **Input**: 表单输入组件
- **Modal**: 模态对话框组件
- **Toast**: 消息通知组件

### 工作区组件
- **ProjectTree**: 项目资源树
- **CodeEditor**: 代码编辑器
- **TabBar**: 标签页管理
- **DiagramPreview**: 图形预览
- **ProblemsPanel**: 问题诊断面板

## 🔧 开发模式

当前版本使用模拟数据进行开发和演示：

### 模拟功能
- ✅ 用户认证（注册/登录）
- ✅ 项目 CRUD 操作
- ✅ 代码语法验证
- ✅ SVG 图形生成
- ✅ AI 代码生成
- ✅ 实时错误诊断

### 示例数据
- 预置示例项目（智能汽车系统、航空航天系统）
- 完整的 SysML v2 代码示例
- 精美的 SVG 图形演示

## 🚧 开发计划

### 短期目标
- [ ] 集成真实的 Monaco Editor
- [ ] 实现 SysML v2 语法高亮
- [ ] 连接后端 API 服务
- [ ] 添加文件导入/导出功能

### 长期目标
- [ ] 实时协作编辑
- [ ] 版本控制集成
- [ ] 插件系统支持
- [ ] 移动端适配

## 📖 使用指南

详细的使用说明请参考：
- [开发指南](./DEVELOPMENT_GUIDE.md)
- [项目结构说明](./PROJECT_STRUCTURE.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

---

**SysML v2 建模平台** - 让系统建模更简单、更高效！
