# SysML v2 建模平台 - 项目结构

这是一个基于 Next.js 和 React 的 SysML v2 建模工具前端项目。

## 项目结构

```
src/
├── app/                    # Next.js App Router 页面
│   ├── login/             # 登录页面
│   ├── register/          # 注册页面
│   ├── workspace/         # 工作区页面
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 首页（重定向）
│   └── globals.css        # 全局样式
├── components/            # React 组件
│   ├── auth/              # 认证相关组件
│   │   ├── LoginPage.tsx
│   │   └── RegisterPage.tsx
│   ├── ui/                # 通用 UI 组件
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── Modal.tsx
│   │   └── Toast.tsx
│   └── workspace/         # 工作区组件
│       ├── WorkspacePage.tsx
│       ├── ProjectTree.tsx
│       ├── CodeEditor.tsx
│       ├── TabBar.tsx
│       ├── DiagramPreview.tsx
│       ├── ProblemsPanel.tsx
│       ├── ModalDialogs.tsx
│       └── ContextMenu.tsx
├── contexts/              # React Context
│   └── AppContext.tsx     # 应用状态管理
├── types/                 # TypeScript 类型定义
│   └── index.ts
└── utils/                 # 工具函数
    ├── api.ts             # API 调用
    ├── validation.ts      # 表单验证
    └── helpers.ts         # 通用工具函数
```

## 主要功能

### 1. 用户认证
- 用户注册和登录
- 会话管理
- 自动重定向

### 2. 项目管理
- 创建、重命名、删除项目
- 项目树形结构展示
- 命名空间管理

### 3. 代码编辑
- 基于 Monaco Editor 的代码编辑器
- 语法高亮和代码补全
- 多标签页支持
- 实时语法验证

### 4. 图形预览
- SVG 图形渲染
- 缩放和平移功能
- 自动布局

### 5. AI 功能
- AI 代码生成
- 智能补全建议

## 技术栈

- **框架**: Next.js 15 (App Router)
- **UI 库**: React 19
- **样式**: Tailwind CSS 4
- **语言**: TypeScript
- **状态管理**: React Context + useReducer
- **代码编辑器**: Monaco Editor (计划集成)
- **图形渲染**: SVG

## 开发指南

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 页面路由

- `/` - 首页（自动重定向）
- `/login` - 登录页面
- `/register` - 注册页面
- `/workspace` - 主工作区

## 组件设计原则

1. **模块化**: 每个组件职责单一，可复用
2. **类型安全**: 使用 TypeScript 确保类型安全
3. **响应式**: 支持不同屏幕尺寸
4. **可访问性**: 遵循 WCAG 指南
5. **性能优化**: 使用 React.memo 和 useMemo 优化性能

## 状态管理

使用 React Context + useReducer 模式管理全局状态：

- `auth`: 用户认证状态
- `projects`: 项目数据
- `editor`: 编辑器状态
- `contextMenu`: 上下文菜单
- `modal`: 模态对话框
- `diagnostics`: 代码诊断信息

## API 集成

API 调用统一通过 `utils/api.ts` 处理：

- 认证 API
- 项目管理 API
- SysML 解析和生成 API
- AI 服务 API

## 样式系统

使用 Tailwind CSS 和自定义 CSS 类：

- 全局样式定义在 `globals.css`
- 组件样式使用 Tailwind 类
- IDE 布局使用自定义 CSS 类

## 下一步开发

1. 集成真实的 Monaco Editor
2. 实现 SysML v2 语法高亮
3. 连接后端 API
4. 添加更多图形渲染功能
5. 实现协作编辑功能
