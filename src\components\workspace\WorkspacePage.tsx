'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useApp } from '@/contexts/AppContext';
import { useToast } from '@/components/ui/Toast';
import { authApi, removeAuthToken } from '@/utils/api';
import ProjectTree from './ProjectTree';
import TabBar from './TabBar';
import CodeEditor from './CodeEditor';
import DiagramPreview from './DiagramPreview';
import ProblemsPanel from './ProblemsPanel';
import ModalDialogs from './ModalDialogs';
import ContextMenu from './ContextMenu';
import { TreeNode, EditorTab } from '@/types';
import { generateId } from '@/utils/helpers';

const WorkspacePage: React.FC = () => {
  const router = useRouter();
  const { state, dispatch } = useApp();
  const { showToast } = useToast();

  // 检查认证状态
  useEffect(() => {
    if (!state.auth.isLoading && !state.auth.isAuthenticated) {
      router.push('/login');
    }
  }, [state.auth.isAuthenticated, state.auth.isLoading, router]);

  // 处理节点选择
  const handleNodeSelect = (node: TreeNode) => {
    dispatch({ type: 'SET_SELECTED_PROJECT', payload: node.type === 'project' ? node.id : null });
  };

  // 处理节点双击（打开文件）
  const handleNodeDoubleClick = (node: TreeNode) => {
    if (node.type === 'diagram') {
      // 检查是否已经打开
      const existingTab = state.editor.tabs.find(tab => tab.diagramId === node.id);
      
      if (existingTab) {
        // 切换到已存在的标签页
        dispatch({ type: 'SET_ACTIVE_TAB', payload: existingTab.id });
      } else {
        // 创建新标签页
        const newTab: EditorTab = {
          id: generateId(),
          title: node.name,
          content: '', // 这里应该从API加载内容
          isDirty: false,
          diagramId: node.id,
        };
        
        dispatch({ type: 'ADD_EDITOR_TAB', payload: newTab });
      }
    }
  };

  // 处理退出登录
  const handleLogout = async () => {
    try {
      await authApi.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      removeAuthToken();
      dispatch({ type: 'SET_AUTH_USER', payload: null });
      router.push('/login?logged_out=true');
    }
  };

  // 工具栏按钮处理
  const handleNewProject = () => {
    dispatch({
      type: 'SET_MODAL',
      payload: {
        isOpen: true,
        type: 'create-project',
      },
    });
  };

  const handleSave = () => {
    // 保存当前活动文件
    const activeTab = state.editor.tabs.find(tab => tab.id === state.editor.activeTabId);
    if (activeTab) {
      // 这里应该调用保存逻辑
      showToast({
        type: 'info',
        message: '保存功能将在编辑器组件中实现',
        duration: 2000,
      });
    }
  };

  const handleAIGenerate = () => {
    showToast({
      type: 'info',
      message: 'AI 生成功能将在编辑器组件中实现',
      duration: 2000,
    });
  };

  const handleGenerateDiagram = () => {
    showToast({
      type: 'info',
      message: '生成图功能将在编辑器组件中实现',
      duration: 2000,
    });
  };

  // 获取当前活动标签页
  const activeTab = state.editor.tabs.find(tab => tab.id === state.editor.activeTabId) || null;

  // 如果正在加载，显示加载界面
  if (state.auth.isLoading) {
    return (
      <div className="ide-layout">
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">正在加载工作区...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="ide-layout">
      {/* 顶部工具栏 */}
      <header className="ide-header">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <h1 className="text-lg font-semibold text-gray-900">SysML v2 建模平台</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleNewProject}
              className="btn btn-ghost"
              title="新建项目"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              新建项目
            </button>
            
            <button
              onClick={handleSave}
              className="btn btn-ghost"
              title="保存 (Ctrl+S)"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              保存
            </button>
            
            <button
              onClick={handleAIGenerate}
              className="btn btn-ghost"
              title="AI 生成"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              AI 生成
            </button>
            
            <button
              onClick={handleGenerateDiagram}
              className="btn btn-ghost"
              title="生成图"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              生成图
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">
            欢迎，{state.auth.user?.username}
          </span>
          
          <div className="relative">
            <button
              onClick={handleLogout}
              className="btn btn-ghost"
              title="退出登录"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              退出登录
            </button>
          </div>
        </div>
      </header>

      {/* 主体区域 */}
      <div className="ide-body">
        {/* 左侧边栏 - 项目树 */}
        <aside className="ide-sidebar">
          <ProjectTree
            onNodeSelect={handleNodeSelect}
            onNodeDoubleClick={handleNodeDoubleClick}
          />
        </aside>

        {/* 主编辑区域 */}
        <main className="ide-main">
          <div className="ide-editor">
            <TabBar />
            <CodeEditor activeTab={activeTab} />
          </div>
          
          {/* 问题面板 */}
          <ProblemsPanel />
        </main>

        {/* 右侧预览区域 */}
        <aside className="ide-preview">
          <DiagramPreview />
        </aside>
      </div>

      {/* 状态栏 */}
      <div className="ide-status-bar">
        <div className="flex items-center space-x-4">
          <span>就绪</span>
          {activeTab && (
            <span>
              {activeTab.title} {activeTab.isDirty ? '(未保存)' : ''}
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-4">
          <span>SysML v2</span>
          <span>行 1, 列 1</span>
        </div>
      </div>

      {/* 模态对话框 */}
      <ModalDialogs />
      
      {/* 上下文菜单 */}
      <ContextMenu />
    </div>
  );
};

export default WorkspacePage;
